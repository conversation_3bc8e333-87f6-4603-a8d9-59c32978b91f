<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Sidebar Catégories</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
            position: relative;
            min-height: 300px;
        }
        
        .before {
            border-color: #e74c3c;
            background: #fdf2f2;
        }
        
        .after {
            border-color: #27ae60;
            background: #f2fdf2;
        }
        
        .demo-layout {
            position: relative;
            height: 250px;
            background: #ecf0f1;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .demo-header {
            height: 40px;
            background: #3498db;
            color: white;
            display: flex;
            align-items: center;
            padding: 0 15px;
            font-weight: 600;
        }
        
        .demo-sidebar {
            position: absolute;
            left: 0;
            top: 40px;
            bottom: 0;
            width: 150px;
            background: #2c3e50;
            padding: 10px;
        }
        
        .demo-category {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
            padding: 8px;
            margin: 2px 0;
            border-radius: 4px;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .demo-category.active {
            background: #3498db;
            color: white;
        }
        
        .demo-content {
            position: absolute;
            left: 150px;
            top: 40px;
            right: 0;
            bottom: 0;
            padding: 15px;
            background: white;
        }
        
        .demo-bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: #2c3e50;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 10px;
        }
        
        .demo-nav-item {
            color: #bdc3c7;
            font-size: 10px;
            text-align: center;
            padding: 5px;
        }
        
        .demo-nav-item.active {
            color: #3498db;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        
        .change-list {
            list-style: none;
            padding: 0;
        }
        
        .change-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .change-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .feature-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Démonstration - Nouvelle Sidebar Catégories</h1>
        
        <div class="demo-section">
            <h2>📋 Résumé des modifications</h2>
            <div class="highlight success">
                <strong>Objectifs atteints :</strong>
                <ul>
                    <li>❌ Suppression de la catégorie "BESTSELLER"</li>
                    <li>📍 Déplacement des catégories vers une sidebar à gauche</li>
                    <li>📱 Interface responsive pour mobile et desktop</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔄 Comparaison Avant/Après</h2>
            
            <div class="comparison">
                <div class="before">
                    <h3>❌ AVANT - Navigation en bas</h3>
                    <div class="demo-layout">
                        <div class="demo-header">🍽️ POS Interface</div>
                        <div class="demo-content">
                            <div style="background: #ecf0f1; height: 100%; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666;">
                                Zone des articles
                            </div>
                        </div>
                        <div class="demo-bottom-nav">
                            <div class="demo-nav-item active">⭐<br>BESTSELLER</div>
                            <div class="demo-nav-item">☕<br>CAFÉ</div>
                            <div class="demo-nav-item">🥪<br>SANDWICH</div>
                            <div class="demo-nav-item">🍰<br>DESSERT</div>
                            <div class="demo-nav-item">🍽️<br>COMBO</div>
                        </div>
                    </div>
                </div>
                
                <div class="after">
                    <h3>✅ APRÈS - Sidebar à gauche</h3>
                    <div class="demo-layout">
                        <div class="demo-header">🍽️ POS Interface</div>
                        <div class="demo-sidebar">
                            <div class="demo-category active">🍽️ Tous les articles</div>
                            <div class="demo-category">☕ Café</div>
                            <div class="demo-category">🥪 Sandwich</div>
                            <div class="demo-category">🍰 Dessert</div>
                            <div class="demo-category">🍽️ Combo</div>
                            <div class="demo-category">📋 Autres</div>
                        </div>
                        <div class="demo-content">
                            <div style="background: #ecf0f1; height: 100%; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #666;">
                                Zone des articles<br>
                                <small>(Plus d'espace disponible)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 Nouvelles fonctionnalités</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-title">🗂️ Sidebar fixe</div>
                    <div class="feature-desc">Navigation des catégories toujours visible à gauche, plus d'espace pour les articles</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-title">📱 Responsive mobile</div>
                    <div class="feature-desc">Sidebar cachée sur mobile avec bouton toggle pour l'ouvrir/fermer</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-title">🎨 Design amélioré</div>
                    <div class="feature-desc">Interface plus moderne avec catégories en liste verticale</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-title">🚫 Pas de BESTSELLER</div>
                    <div class="feature-desc">Catégorie "BESTSELLER" supprimée, remplacée par "Tous les articles"</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔧 Modifications techniques</h2>
            
            <ul class="change-list">
                <li><strong>CSS :</strong> Création de .categories-sidebar avec position fixe à gauche</li>
                <li><strong>Layout :</strong> Ajustement du padding-left du main-content (220px)</li>
                <li><strong>HTML :</strong> Remplacement de la structure bottom-nav par sidebar</li>
                <li><strong>Responsive :</strong> Media queries pour mobile avec toggle button</li>
                <li><strong>JavaScript :</strong> Fonctions toggleCategories() et gestion des clics</li>
                <li><strong>Suppression :</strong> Bouton BESTSELLER remplacé par "Tous les articles"</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>📱 Comportement mobile</h2>
            
            <div class="highlight">
                <strong>Sur mobile (≤ 768px) :</strong>
                <ul>
                    <li>🔘 Bouton toggle flottant à gauche pour ouvrir la sidebar</li>
                    <li>📱 Sidebar en overlay avec animation slide</li>
                    <li>👆 Fermeture automatique en cliquant en dehors</li>
                    <li>🛒 Panier repositionné en bas avec toggle</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2>🚀 Comment tester</h2>
            
            <div class="highlight success">
                <p><strong>Ouvrez <code>pos_mobile.php</code> pour voir les changements :</strong></p>
                <ul>
                    <li>✅ Sidebar des catégories à gauche (desktop)</li>
                    <li>✅ Plus de catégorie "BESTSELLER"</li>
                    <li>✅ Bouton toggle sur mobile</li>
                    <li>✅ Interface responsive</li>
                    <li>✅ Plus d'espace pour les articles</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
