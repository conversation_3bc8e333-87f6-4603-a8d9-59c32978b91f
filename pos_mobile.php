<?php
require_once 'pos_config.php';

// Gestion des actions AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    $action = $_POST['action'] ?? '';
    $response = ['success' => false, 'message' => ''];

    switch ($action) {
        case 'add_to_cart':
            $articleId = intval($_POST['article_id']);
            $quantity = intval($_POST['quantity'] ?? 1);

            $result = $pos->addToCart($articleId, $quantity);
            if ($result['success']) {
                $cart = $pos->getCart();
                $response['success'] = true;
                $response['cart_count'] = count($cart);
                $response['cart_total'] = formatPrice($pos->getCartTotalTTC());
                $response['message'] = $result['message'];
                $response['stock_insufficient'] = $result['stock_insufficient'] ?? false;

                // Debug côté serveur
                error_log("DEBUG add_to_cart: Article $articleId ajouté, panier contient maintenant " . count($cart) . " articles");
            } else {
                $response['message'] = $result['message'];
                error_log("DEBUG add_to_cart: ERREUR - " . $result['message']);
            }
            break;

        case 'update_cart':
            $articleId = intval($_POST['article_id']);
            $quantity = intval($_POST['quantity']);

            if ($pos->updateCartQuantity($articleId, $quantity)) {
                $response['success'] = true;
                $response['cart_total'] = formatPrice($pos->getCartTotalTTC());
                $response['cart_count'] = count($pos->getCart());
                $response['message'] = 'Quantité mise à jour';
            }
            break;

        case 'remove_from_cart':
            $articleId = intval($_POST['article_id']);

            if ($pos->removeFromCart($articleId)) {
                $response['success'] = true;
                $response['cart_count'] = count($pos->getCart());
                $response['cart_total'] = formatPrice($pos->getCartTotalTTC());
            }
            break;

        case 'process_order':
            $paymentMethod = $_POST['payment_method'] ?? 'cash';

            $orderId = $pos->processOrder($paymentMethod);
            if ($orderId) {
                $response['success'] = true;
                $response['order_id'] = $orderId;
                $response['message'] = 'Commande validée !';
            } else {
                $response['message'] = 'Erreur lors de la validation';
            }
            break;

        case 'clear_cart':
            $pos->clearCart();
            $response['success'] = true;
            $response['message'] = 'Panier vidé';
            break;

        case 'get_cart_data':
            $cart = $pos->getCart();
            $response['success'] = true;
            $response['cart'] = $cart;
            $response['total'] = $pos->getCartTotalTTC();
            $response['count'] = count($cart);

            // Debug côté serveur
            error_log("DEBUG get_cart_data: " . json_encode([
                'cart_count' => count($cart),
                'cart_keys' => array_keys($cart),
                'total' => $response['total']
            ]));
            break;
    }

    echo json_encode($response);
    exit;
}

// Récupération des données pour l'affichage
$categories = $pos->getCategories();
$selectedCategory = $_GET['category'] ?? '';
$searchQuery = $_GET['search'] ?? '';

if ($searchQuery) {
    $articles = $pos->searchArticles($searchQuery);
} else {
    $articles = $pos->getArticlesByCategory($selectedCategory ?: null);
}

$cart = $pos->getCart();
$cartTotal = $pos->getCartTotalTTC();
$todayStats = $pos->getTodayStats();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo POS_NAME; ?> - Point de Vente</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="POS">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mobile-menu-icon {
            display: none;
            background: none;
            border: none;
            font-size: 24px;
            color: #2c3e50;
            cursor: pointer;
            padding: 5px;
        }

        .header-menu {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .menu-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: #666;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 11px;
            min-width: 60px;
        }

        .menu-btn:hover {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }

        .menu-btn.active {
            background: #3498db;
            color: white;
        }

        .menu-btn i {
            font-size: 16px;
        }

        .menu-btn span {
            font-weight: 500;
        }

        /* Search Bar */
        .search-container {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            outline: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .search-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            font-size: 18px;
            cursor: pointer;
        }

        /* Categories Sidebar - Left */
        .categories-sidebar {
            position: fixed;
            left: 0;
            top: 50px;
            bottom: 0;
            width: 200px;
            background: #2c3e50;
            padding: 20px 0;
            z-index: 50;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            transform: translateX(-100%); /* Fermée par défaut sur mobile */
            transition: transform 0.3s ease;
        }

        .categories-sidebar.mobile-open {
            transform: translateX(0);
        }

        .categories-list {
            display: flex;
            flex-direction: column;
            gap: 5px;
            padding: 0 10px;
        }

        .category-btn {
            width: 100%;
            padding: 15px 12px;
            border: none;
            background: transparent;
            color: #bdc3c7;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-radius: 8px;
            margin-bottom: 2px;
        }

        .category-btn.active {
            color: #ffffff;
            background: #3498db;
        }

        .category-btn:hover {
            color: #ffffff;
            background: rgba(52, 152, 219, 0.7);
        }

        .category-icon {
            font-size: 18px;
            min-width: 20px;
        }

        .category-name {
            font-weight: 600;
        }

        /* Main Content */
        .main-content {
            display: flex;
            gap: 20px;
            padding: 0 20px 20px 220px; /* Left padding for sidebar */
            min-height: calc(100vh - 100px);
        }

        /* Articles Grid */
        .articles-section {
            flex: 2;
            padding-top: 5px;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 12px;
        }

        .article-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            height: 200px;
        }

        .article-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .article-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .article-icon {
            font-size: 40px;
            color: white;
        }

        .article-price-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .article-info {
            padding: 12px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .article-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.2;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .article-category {
            font-size: 11px;
            color: #7f8c8d;
            margin-bottom: 8px;
        }

        .article-stock {
            font-size: 11px;
            color: #27ae60;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .article-stock.insufficient {
            color: #e74c3c;
        }

        .stock-warning-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            z-index: 2;
        }

        .article-card.insufficient-stock {
            border: 2px solid #e74c3c;
            opacity: 0.8;
        }

        .article-card.insufficient-stock .article-image {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .add-btn {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: #3498db;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .add-btn:hover {
            background: #2980b9;
            transform: scale(1.1);
        }







        /* Cart Section - Nouvelle version */
        .cart-section {
            flex: 1;
            min-width: 350px;
            max-width: 400px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            height: fit-content;
            position: sticky;
            top: 100px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }

        .cart-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .cart-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .cart-subtitle {
            font-size: 14px;
            color: #666;
        }

        .cart-items {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 0 20px;
        }

        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .cart-item-info {
            flex: 1;
        }

        .cart-item-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .cart-item-price {
            color: #666;
            font-size: 14px;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .qty-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            background: #f0f0f0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .qty-btn:hover {
            background: #3498db;
            color: white;
        }

        .quantity {
            font-weight: 600;
            min-width: 30px;
            text-align: center;
        }

        .cart-total {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px;
            text-align: center;
            flex-shrink: 0;
            border: 2px solid #e9ecef;
            position: relative;
            z-index: 9999;
        }

        .total-amount {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
        }

        .cart-actions {
            display: flex;
            gap: 10px;
            padding: 20px;
            background: white;
            border-top: 1px solid #f0f0f0;
            flex-shrink: 0;
            position: sticky;
            bottom: 0;
            z-index: 9999;
        }

        .action-btn {
            flex: 1;
            padding: 18px;
            border: none;
            border-radius: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            display: flex !important;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 50px;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative;
            z-index: 9999;
        }

        .btn-validate {
            background: #28a745;
            color: white;
        }

        .btn-validate:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .btn-cancel {
            background: #dc3545;
            color: white;
        }

        .btn-cancel:hover {
            background: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .empty-cart {
            text-align: center;
            padding: 40px 20px;
            color: #95a5a6;
        }

        .empty-cart-icon {
            font-size: 60px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* Cart Footer Mobile */
        .cart-footer-mobile {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 2px solid #f0f0f0;
            z-index: 99999;
            display: none; /* Masqué par défaut */
            flex-direction: column;
            box-shadow: 0 -4px 15px rgba(0,0,0,0.1);
        }

        .cart-footer-mobile.show {
            display: flex;
        }

        .cart-footer-total {
            padding: 15px 20px;
            text-align: center;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .cart-footer-total .total-amount {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
        }

        .cart-footer-buttons {
            display: flex;
            gap: 15px;
            padding: 15px 20px 25px 20px; /* Plus de padding en bas pour la barre de navigation */
            background: white;
        }

        .cart-footer-btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-weight: 700;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .cart-footer-btn.validate {
            background: #28a745;
            color: white;
        }

        .cart-footer-btn.validate:hover {
            background: #218838;
        }

        .cart-footer-btn.cancel {
            background: #dc3545;
            color: white;
        }

        .cart-footer-btn.cancel:hover {
            background: #c82333;
        }

        /* Empty States */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.8);
        }

        .empty-icon {
            font-size: 60px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* Empty cart state */
        .empty-cart {
            text-align: center;
            padding: 40px 20px;
            color: #95a5a6;
            background: white;
        }

        .empty-cart-icon {
            font-size: 60px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                padding: 0 20px 120px 20px; /* Padding égal sur mobile */
            }

            .cart-section {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                transform: translateY(calc(100% - 70px)); /* Fermée par défaut */
                transition: transform 0.3s ease;
                z-index: 1000;
                height: calc(100vh - 20px); /* Laisser 20px en bas */
                max-height: calc(100vh - env(safe-area-inset-bottom, 20px));
                border-radius: 20px 20px 0 0;
                box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
                display: flex;
                flex-direction: column;
                overflow: hidden;
                padding-bottom: env(safe-area-inset-bottom, 0px);
            }

            .cart-section.expanded {
                transform: translateY(0);
            }

            /* Forcer l'affichage des boutons sur mobile */
            .cart-actions {
                display: flex !important;
                position: fixed !important;
                bottom: 0 !important;
                left: 0 !important;
                right: 0 !important;
                z-index: 9999 !important;
                background: white !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .action-btn {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            .cart-toggle-btn {
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 60px;
                background: #3498db;
                color: white;
                border: none;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                z-index: 999;
                box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
                transition: all 0.3s ease;
            }

            .cart-toggle-btn:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 20px rgba(52, 152, 219, 0.6);
            }

            .cart-toggle-btn .cart-count {
                position: absolute;
                top: -5px;
                right: -5px;
                background: #e74c3c;
                color: white;
                border-radius: 50%;
                width: 25px;
                height: 25px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: 600;
            }

            .cart-header-mobile {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #f0f0f0;
                background: white;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            .cart-close-btn {
                background: none;
                border: none;
                font-size: 24px;
                color: #666;
                cursor: pointer;
                padding: 5px;
            }









            .articles-grid {
                grid-template-columns: 1fr 1fr; /* Exactement 2 colonnes */
                gap: 10px;
            }

            .header-menu {
                gap: 5px;
            }

            .menu-btn {
                min-width: 50px;
                padding: 6px 8px;
                font-size: 10px;
            }

            .menu-btn i {
                font-size: 14px;
            }

            .menu-btn span {
                display: none;
            }

            .search-container {
                padding: 15px;
            }

            .header {
                padding: 10px 15px;
            }

            .logo {
                display: none;
            }

            .mobile-menu-icon {
                display: block;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Success Animation */
        .success-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(40, 167, 69, 0.95);
            color: white;
            padding: 20px 40px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            animation: successPop 0.5s ease-out;
        }

        @keyframes successPop {
            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }

        /* Warning Animation */
        .warning-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 193, 7, 0.95);
            color: #212529;
            padding: 20px 40px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            animation: warningPop 0.5s ease-out;
            border: 2px solid #ffc107;
        }

        @keyframes warningPop {
            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }



        @media (min-width: 769px) {
            .mobile-menu-icon {
                display: none;
            }

            .cart-toggle-btn {
                display: none;
            }

            .cart-section {
                position: sticky;
                top: 100px;
                transform: none;
                height: fit-content;
                border-radius: 15px;
                box-shadow: 0 2px 15px rgba(0,0,0,0.1);
                max-height: none;
                display: block;
                overflow: visible;
            }

            .cart-items {
                max-height: 400px;
                padding: 0;
            }

            .cart-total {
                margin: 0 0 20px 0;
            }

            .cart-header-mobile {
                display: none;
            }

            .cart-header {
                display: block;
            }

            .cart-actions {
                position: static;
                margin-bottom: 0;
            }

            /* Sidebar toujours visible sur desktop */
            .categories-sidebar {
                transform: translateX(0) !important;
            }

            /* Masquer le cart-footer-mobile sur desktop */
            .cart-footer-mobile {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <button class="mobile-menu-icon" onclick="toggleCategories()">
                <i class="fas fa-bars"></i>
            </button>
            <div class="logo">
                <i class="fas fa-coffee"></i>
            </div>
            <div class="header-menu">
                <button class="menu-btn active" onclick="selectMenu('commandes')">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Commandes</span>
                </button>
                <button class="menu-btn" onclick="selectMenu('historique')">
                    <i class="fas fa-history"></i>
                    <span>Historique</span>
                </button>
                <button class="menu-btn" onclick="selectMenu('analyses')">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analyses</span>
                </button>
                <button class="menu-btn" onclick="selectMenu('parametres')">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </button>
            </div>
        </div>
    </div>





    <!-- Main Content -->
    <div class="main-content">
        <!-- Articles Section -->
        <div class="articles-section">
            <?php if (empty($articles)): ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>Aucun article trouvé</h3>
                    <p>Essayez de modifier votre recherche ou sélectionnez une autre catégorie</p>
                </div>
            <?php else: ?>
                <div class="articles-grid">
                    <?php foreach ($articles as $article): ?>
                        <?php
                        $isStockInsufficient = $article['quantite'] <= 0;
                        $cardClass = $isStockInsufficient ? 'article-card insufficient-stock' : 'article-card';
                        $stockClass = $isStockInsufficient ? 'article-stock insufficient' : 'article-stock';
                        ?>
                        <div class="<?php echo $cardClass; ?>" onclick="addToCart(<?php echo $article['IDarticles']; ?>)">
                            <?php if ($isStockInsufficient): ?>
                                <div class="stock-warning-badge">Rupture</div>
                            <?php endif; ?>
                            <div class="article-image">
                                <div class="article-icon">
                                    <?php
                                    $icon = '🍽️';
                                    $name = strtolower($article['designation']);
                                    if (strpos($name, 'café') !== false || strpos($name, 'coffee') !== false) $icon = '☕';
                                    elseif (strpos($name, 'thé') !== false || strpos($name, 'tea') !== false) $icon = '🍵';
                                    elseif (strpos($name, 'sandwich') !== false) $icon = '🥪';
                                    elseif (strpos($name, 'salade') !== false) $icon = '🥗';
                                    elseif (strpos($name, 'dessert') !== false || strpos($name, 'gâteau') !== false) $icon = '🍰';
                                    elseif (strpos($name, 'glace') !== false) $icon = '🍦';
                                    echo $icon;
                                    ?>
                                </div>
                                <div class="article-price-badge"><?php echo formatPrice($pos->getArticlePrice($article['IDarticles'])); ?></div>
                            </div>
                            <div class="article-info">
                                <div>
                                    <div class="article-name"><?php echo htmlspecialchars($article['designation']); ?></div>
                                    <div class="article-category"><?php echo htmlspecialchars($article['nom_categorie']); ?></div>
                                </div>
                                <div class="<?php echo $stockClass; ?>">
                                    <i class="fas fa-box"></i>
                                    <?php echo $article['quantite']; ?>
                                </div>
                            </div>
                            <button class="add-btn" onclick="event.stopPropagation(); addToCart(<?php echo $article['IDarticles']; ?>)">
                                <i class="fas fa-shopping-cart"></i>
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Bouton Panier Flottant -->
        <button class="cart-toggle-btn" onclick="toggleCart()" id="cartToggleBtn">
            <i class="fas fa-shopping-cart"></i>
            <span class="cart-count" id="cartCount" style="display: <?php echo !empty($cart) ? 'flex' : 'none'; ?>">
                <?php echo count($cart); ?>
            </span>
        </button>

        <!-- Cart Section -->
        <div class="cart-section" id="cartSection">
            <div class="cart-header-mobile">
                <div>
                    <div class="cart-title">Commande Actuelle</div>
                    <div class="cart-subtitle">Table 103</div>
                </div>
                <button class="cart-close-btn" onclick="toggleCart()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="cart-header" style="display: none;">
                <div class="cart-title">Commande Actuelle</div>
                <div class="cart-subtitle">Table 103</div>
            </div>

            <div class="cart-items" id="cartItems">
                <?php if (empty($cart)): ?>
                    <div class="empty-cart">
                        <div class="empty-cart-icon">🛒</div>
                        <p>Sélectionnez des articles pour commencer</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($cart as $articleId => $item): ?>
                        <div class="cart-item" data-article-id="<?php echo $articleId; ?>">
                            <div class="cart-item-info">
                                <div class="cart-item-name"><?php echo htmlspecialchars($item['article']['designation']); ?></div>
                                <div class="cart-item-price"><?php echo formatPrice($item['price']); ?> × <?php echo $item['quantity']; ?></div>
                            </div>
                            <div class="quantity-controls">
                                <button class="qty-btn" onclick="updateQuantity(<?php echo $articleId; ?>, <?php echo $item['quantity'] - 1; ?>)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span class="quantity"><?php echo $item['quantity']; ?></span>
                                <button class="qty-btn" onclick="updateQuantity(<?php echo $articleId; ?>, <?php echo $item['quantity'] + 1; ?>)">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <?php if (!empty($cart)): ?>
                <!-- Total -->
                <div class="cart-total">
                    <div class="total-amount" id="cartTotal"><?php echo formatPrice($cartTotal); ?></div>
                </div>

                <!-- Boutons -->
                <div class="cart-actions">
                    <button class="action-btn btn-validate" onclick="processPayment('confirm')">
                        <i class="fas fa-check"></i> Valider
                    </button>
                    <button class="action-btn btn-cancel" onclick="clearCart()">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                </div>
            <?php endif; ?>


        </div>

    </div>





    <!-- Cart Footer Mobile -->
    <div class="cart-footer-mobile" id="cartFooterMobile">
        <div class="cart-footer-total">
            <div class="total-amount" id="mobileCartTotal"><?php echo formatPrice($cartTotal); ?></div>
        </div>
        <div class="cart-footer-buttons">
            <button class="cart-footer-btn validate" onclick="processPayment('confirm')">
                <i class="fas fa-check"></i> Valider
            </button>
            <button class="cart-footer-btn cancel" onclick="clearCart()">
                <i class="fas fa-times"></i> Annuler
            </button>
        </div>
    </div>
    <!-- Categories Sidebar -->
    <div class="categories-sidebar" id="categoriesSidebar">
        <div class="categories-list">
            <button class="category-btn <?php echo empty($selectedCategory) ? 'active' : ''; ?>"
                    onclick="selectCategory('')">
                <div class="category-icon">⭐</div>
                <div class="category-name">Tous les articles</div>
            </button>

            <?php foreach ($categories as $category): ?>
                <button class="category-btn <?php echo $selectedCategory == $category['IDCategorie'] ? 'active' : ''; ?>"
                        onclick="selectCategory(<?php echo $category['IDCategorie']; ?>)">
                    <div class="category-icon">⭐</div>
                    <div class="category-name"><?php echo htmlspecialchars($category['categories']); ?></div>
                </button>
            <?php endforeach; ?>

            <button class="category-btn" onclick="selectCategory('combo')">
                <div class="category-icon">⭐</div>
                <div class="category-name">Combo</div>
            </button>

            <button class="category-btn" onclick="selectCategory('other')">
                <div class="category-icon">⭐</div>
                <div class="category-name">Autres</div>
            </button>
        </div>
    </div>

    <script>
        // Variables globales
        let isLoading = false;

        // Fonctions de navigation
        function selectCategory(categoryId) {
            window.location.href = `?category=${categoryId}`;
        }

        function selectMenu(menuItem) {
            // Retirer la classe active de tous les boutons
            document.querySelectorAll('.menu-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Ajouter la classe active au bouton cliqué
            event.target.closest('.menu-btn').classList.add('active');

            // Gérer la navigation selon le menu sélectionné
            switch(menuItem) {
                case 'commandes':
                    // Rester sur la page actuelle (POS)
                    break;
                case 'historique':
                    // Rediriger vers la page historique
                    window.location.href = 'historique.php';
                    break;
                case 'analyses':
                    // Rediriger vers la page analyses
                    window.location.href = 'analyses.php';
                    break;
                case 'parametres':
                    // Rediriger vers la page paramètres
                    window.location.href = 'parametres.php';
                    break;
            }
        }

        function performSearch() {
            const searchValue = document.getElementById('searchInput').value;
            window.location.href = `?search=${encodeURIComponent(searchValue)}`;
        }

        // Gestion du panier
        function addToCart(articleId, quantity = 1) {
            if (isLoading) return;

            isLoading = true;
            const btn = event.target.closest('.article-card').querySelector('.add-btn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<div class="loading"></div>';

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=add_to_cart&article_id=${articleId}&quantity=${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Mise à jour immédiate du count
                    if (data.cart_count !== undefined) {
                        updateCartCount(data.cart_count);
                    }
                    // Puis mise à jour complète
                    updateCartDisplay();
                    // Afficher seulement les warnings (stock insuffisant)
                    if (data.stock_insufficient) {
                        showWarning(data.message || 'Stock insuffisant');
                    }
                } else {
                    showError(data.message || 'Erreur lors de l\'ajout');
                }
            })
            .catch(error => {
                showError('Erreur de connexion');
            })
            .finally(() => {
                isLoading = false;
                btn.innerHTML = originalText;
            });
        }

        function updateQuantity(articleId, newQuantity) {
            console.log('Mise à jour quantité:', articleId, newQuantity);
            if (isLoading) return;

            isLoading = true;

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_cart&article_id=${articleId}&quantity=${newQuantity}`
            })
            .then(response => {
                console.log('Réponse updateQuantity:', response);
                return response.json();
            })
            .then(data => {
                console.log('Données updateQuantity:', data);
                if (data.success) {
                    console.log('Mise à jour réussie');
                    // Mise à jour directe avec les données retournées
                    if (data.cart_count !== undefined) {
                        updateCartCount(data.cart_count);
                    }
                    // Puis mise à jour complète pour synchroniser tout
                    updateCartDisplay();
                } else {
                    console.error('Erreur mise à jour:', data);
                    showError('Erreur lors de la mise à jour');
                }
            })
            .catch(error => {
                console.error('Erreur AJAX updateQuantity:', error);
                showError('Erreur de connexion');
            })
            .finally(() => {
                isLoading = false;
            });
        }

        function processPayment(method) {
            if (isLoading) return;

            if (!confirm('Confirmer la commande ?')) return;

            isLoading = true;

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=process_order&payment_method=${method}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateCartDisplay();
                    // Fermer la cart sur mobile après validation
                    if (window.innerWidth <= 768) {
                        setTimeout(() => {
                            closeCart();
                        }, 500);
                    }
                } else {
                    showError(data.message || 'Erreur lors de la validation');
                }
            })
            .finally(() => {
                isLoading = false;
            });
        }

        function clearCart() {
            if (!confirm('Vider le panier ?')) return;

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=clear_cart'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Mise à jour immédiate du count à 0
                    updateCartCount(0);
                    // Puis mise à jour complète
                    updateCartDisplay();
                    // Fermer la cart sur mobile après vidage
                    if (window.innerWidth <= 768) {
                        closeCart();
                    }
                } else {
                    showError('Erreur lors du vidage du panier');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                showError('Erreur lors du vidage du panier');
            });
        }

        function updateCartDisplay() {
            console.log('Mise à jour de la cart...');

            // Mise à jour AJAX de la cart sans actualisation
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_cart_data'
            })
            .then(response => {
                console.log('Réponse reçue:', response);
                return response.json();
            })
            .then(data => {
                console.log('Données reçues:', data);
                if (data.success) {
                    // Mettre à jour le contenu de la cart
                    updateCartItems(data.cart);
                    updateCartTotal(data.total);
                    updateCartCount(data.count);
                    console.log('Cart mise à jour avec succès');
                } else {
                    console.error('Erreur lors de la mise à jour du panier:', data);
                }
            })
            .catch(error => {
                console.error('Erreur AJAX:', error);
            });
        }

        function updateCartItems(cartData) {
            console.log('=== DEBUT updateCartItems ===');
            console.log('Données reçues:', cartData);
            console.log('Type de cartData:', typeof cartData);
            console.log('Nombre d\'articles:', cartData ? Object.keys(cartData).length : 0);

            const cartItemsContainer = document.getElementById('cartItems');
            console.log('Conteneur cartItems trouvé:', !!cartItemsContainer);

            if (!cartItemsContainer) {
                console.error('ERREUR: Conteneur cartItems non trouvé dans le DOM');
                return;
            }

            if (!cartData || Object.keys(cartData).length === 0) {
                console.log('Panier vide - affichage du message vide');
                cartItemsContainer.innerHTML = `
                    <div class="empty-cart">
                        <div class="empty-cart-icon">🛒</div>
                        <p>Sélectionnez des articles pour commencer</p>
                    </div>
                `;
                console.log('Message vide inséré dans le DOM');
                return;
            }

            console.log('Construction du HTML pour', Object.keys(cartData).length, 'articles');
            let itemsHTML = '';
            let articleCount = 0;

            for (const [articleId, item] of Object.entries(cartData)) {
                articleCount++;
                console.log(`Article ${articleCount}:`, {
                    id: articleId,
                    designation: item.article?.designation,
                    price: item.price,
                    quantity: item.quantity
                });

                const designation = item.article?.designation || 'Article sans nom';
                const price = item.price || 0;
                const quantity = item.quantity || 0;

                itemsHTML += `
                    <div class="cart-item" data-article-id="${articleId}">
                        <div class="cart-item-info">
                            <div class="cart-item-name">${designation}</div>
                            <div class="cart-item-price">${formatPrice(price)} × ${quantity}</div>
                        </div>
                        <div class="quantity-controls">
                            <button class="qty-btn" onclick="updateQuantity(${articleId}, ${quantity - 1})">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span class="quantity">${quantity}</span>
                            <button class="qty-btn" onclick="updateQuantity(${articleId}, ${quantity + 1})">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                `;
            }

            console.log('HTML final généré (longueur):', itemsHTML.length);
            console.log('HTML preview:', itemsHTML.substring(0, 200) + '...');

            cartItemsContainer.innerHTML = itemsHTML;
            console.log('HTML inséré dans le DOM');
            console.log('Contenu final du conteneur:', cartItemsContainer.innerHTML.substring(0, 200) + '...');
            console.log('=== FIN updateCartItems ===');
        }

        function updateCartTotal(total) {
            console.log('Mise à jour du total:', total);
            const cartTotalElements = document.querySelectorAll('#cartTotal, #mobileCartTotal');
            console.log('Éléments total trouvés:', cartTotalElements.length);
            cartTotalElements.forEach(element => {
                if (element) {
                    element.textContent = formatPrice(total);
                    console.log('Total mis à jour:', element.id, formatPrice(total));
                }
            });
        }

        function updateCartCount(count) {
            console.log('Mise à jour du compteur:', count);

            // Mettre à jour le compteur sur le bouton panier
            const cartCountElement = document.getElementById('cartCount');
            if (cartCountElement) {
                cartCountElement.textContent = count;
                cartCountElement.style.display = count > 0 ? 'flex' : 'none';
                console.log('Compteur mis à jour:', cartCountElement.id, count);
            } else {
                console.error('Élément cartCount non trouvé');
            }

            // Mettre à jour le compteur dans le footer mobile s'il existe
            const mobileCartTotal = document.getElementById('mobileCartTotal');
            if (mobileCartTotal) {
                // Le footer mobile affiche le total, pas le count
                console.log('Footer mobile trouvé');
            }
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR'
            }).format(price);
        }

        // Fonctions utilitaires
        function showSuccess(message) {
            const div = document.createElement('div');
            div.className = 'success-animation';
            div.innerHTML = `<i class="fas fa-check"></i> ${message}`;
            document.body.appendChild(div);

            setTimeout(() => {
                div.remove();
            }, 3000);
        }

        function showError(message) {
            alert(message); // Remplacer par une notification plus élégante
        }

        function showWarning(message) {
            const div = document.createElement('div');
            div.className = 'warning-animation';
            div.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
            document.body.appendChild(div);

            setTimeout(() => {
                div.remove();
            }, 4000);
        }

        // Gestion mobile
        function toggleCart() {
            const cartSection = document.getElementById('cartSection');
            const cartFooterMobile = document.getElementById('cartFooterMobile');

            cartSection.classList.toggle('expanded');

            // Afficher/masquer le cart-footer-mobile selon l'état
            if (cartSection.classList.contains('expanded')) {
                cartFooterMobile.classList.add('show');
            } else {
                cartFooterMobile.classList.remove('show');
            }
        }

        // Fonction pour fermer la cart et le footer
        function closeCart() {
            const cartSection = document.getElementById('cartSection');
            const cartFooterMobile = document.getElementById('cartFooterMobile');

            cartSection.classList.remove('expanded');
            cartFooterMobile.classList.remove('show');
        }

        // Fonction pour ouvrir la cart et le footer
        function openCart() {
            const cartSection = document.getElementById('cartSection');
            const cartFooterMobile = document.getElementById('cartFooterMobile');

            cartSection.classList.add('expanded');
            cartFooterMobile.classList.add('show');
        }

        // Vérification automatique de l'état de la cart
        function checkCartState() {
            const cartSection = document.getElementById('cartSection');
            const cartFooterMobile = document.getElementById('cartFooterMobile');

            if (cartSection && cartFooterMobile) {
                if (cartSection.classList.contains('expanded')) {
                    cartFooterMobile.classList.add('show');
                } else {
                    cartFooterMobile.classList.remove('show');
                }
            }
        }

        // Vérifier l'état toutes les 500ms
        setInterval(checkCartState, 500);

        // Fermer le panel des catégories en cliquant en dehors (mobile uniquement)
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 768) {
                const categoriesSidebar = document.getElementById('categoriesSidebar');
                const mobileMenuIcon = document.querySelector('.mobile-menu-icon');

                // Si le panel est ouvert et qu'on clique en dehors
                if (categoriesSidebar && categoriesSidebar.classList.contains('mobile-open')) {
                    if (!categoriesSidebar.contains(event.target) &&
                        !mobileMenuIcon.contains(event.target)) {
                        categoriesSidebar.classList.remove('mobile-open');
                    }
                }
            }
        });

        function toggleCategories() {
            const sidebar = document.getElementById('categoriesSidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // Fermer la sidebar quand on clique en dehors (mobile)
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('categoriesSidebar');
            const toggle = document.querySelector('.mobile-category-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggle.contains(event.target)) {
                sidebar.classList.remove('mobile-open');
            }
        });

        // Gestion responsive
        function handleResize() {
            const cartToggleBtn = document.getElementById('cartToggleBtn');
            const cartSection = document.getElementById('cartSection');

            if (window.innerWidth <= 768) {
                cartToggleBtn.style.display = 'flex';
                // S'assurer que la cart est fermée sur mobile par défaut
                cartSection.classList.remove('expanded');
            } else {
                cartToggleBtn.style.display = 'none';
                // S'assurer que la cart est visible sur desktop
                cartSection.classList.remove('expanded');
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            handleResize();

            // Recherche en temps réel
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (this.value.length >= 2 || this.value.length === 0) {
                        performSearch();
                    }
                }, 500);
            });

            // Gestion du clavier
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });

        window.addEventListener('resize', handleResize);

        // PWA - Service Worker (optionnel)
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('sw.js').catch(console.error);
        }
    </script>
</body>
</html>
