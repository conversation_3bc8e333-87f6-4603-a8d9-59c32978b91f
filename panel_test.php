<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Panel Commande</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
        }
    </style>
</head>
<body>
    
    <h1 style="text-align: center; margin-bottom: 30px; color: #333;">Test Panel Commande - 100% Visible</h1>
    
    <!-- PANEL COMMANDE -->
    <div style="width: 400px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        
        <!-- HEADER -->
        <div style="padding: 25px 25px 20px 25px; border-bottom: 1px solid #f1f3f4;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="color: #2c3e50; font-size: 18px; font-weight: 600; margin: 0;">New Order Bill</h3>
                <span style="background: #667eea; color: white; padding: 5px 12px; border-radius: 20px; font-size: 12px; font-weight: 500;">Order #246</span>
            </div>
        </div>
        
        <!-- ARTICLES -->
        <div style="padding: 20px 25px;">
            
            <!-- ARTICLE 1 -->
            <div style="display: flex; align-items: center; gap: 15px; padding: 15px 0; border-bottom: 1px solid #f1f3f4;">
                <div style="width: 50px; height: 50px; border-radius: 8px; background: linear-gradient(135deg, #ff6b6b, #ffa500); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">🍰</div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: #2c3e50; margin-bottom: 4px;">Cup Cake</div>
                    <div style="color: #667eea; font-weight: 500;">$12.00</div>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="background: #ffa500; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">1</span>
                    <button style="color: #dc3545; background: none; border: none; cursor: pointer; padding: 4px;">🗑️</button>
                </div>
            </div>
            
            <!-- ARTICLE 2 -->
            <div style="display: flex; align-items: center; gap: 15px; padding: 15px 0; border-bottom: 1px solid #f1f3f4;">
                <div style="width: 50px; height: 50px; border-radius: 8px; background: linear-gradient(135deg, #ff6b6b, #ffa500); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">🍕</div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: #2c3e50; margin-bottom: 4px;">Farm Villa</div>
                    <div style="color: #667eea; font-weight: 500;">$16.00</div>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="background: #ffa500; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">2</span>
                    <button style="color: #dc3545; background: none; border: none; cursor: pointer; padding: 4px;">🗑️</button>
                </div>
            </div>
            
        </div>
        
        <!-- RESUME -->
        <div style="padding: 25px; border-top: 1px solid #f1f3f4; background: #f8f9fa;">
            
            <!-- TOTAUX -->
            <div style="margin-bottom: 20px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px; color: #6c757d;">
                    <span>Sub Total</span>
                    <span>$50.00</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 15px; color: #6c757d; font-size: 14px;">
                    <span>Tax 10% (VAT Included)</span>
                    <span>$5.00</span>
                </div>
                <div style="display: flex; justify-content: space-between; font-size: 18px; font-weight: 700; color: #2c3e50; padding-top: 15px; border-top: 1px dashed #ddd;">
                    <span>Total</span>
                    <span style="color: #28a745;">$55.00</span>
                </div>
            </div>
            
            <!-- METHODES PAIEMENT -->
            <div style="margin-bottom: 20px;">
                <div style="font-weight: 600; color: #2c3e50; margin-bottom: 15px;">Payment Method</div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                    <div style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; text-align: center; cursor: pointer; background: white;">
                        <div style="font-size: 20px; margin-bottom: 5px;">💵</div>
                        <span style="font-size: 12px; color: #6c757d;">Cash</span>
                    </div>
                    <div style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; text-align: center; cursor: pointer; background: white;">
                        <div style="font-size: 20px; margin-bottom: 5px;">💳</div>
                        <span style="font-size: 12px; color: #6c757d;">Debit Card</span>
                    </div>
                    <div style="padding: 12px; border: 2px solid #667eea; border-radius: 8px; text-align: center; cursor: pointer; background: #f8f9ff;">
                        <div style="font-size: 20px; margin-bottom: 5px;">📱</div>
                        <span style="font-size: 12px; color: #667eea; font-weight: 600;">E-Wallet</span>
                    </div>
                </div>
            </div>
            
            <!-- BOUTON -->
            <button style="width: 100%; padding: 15px; background: linear-gradient(135deg, #ff6b6b, #ffa500); color: white; border: none; border-radius: 12px; font-size: 16px; font-weight: 600; cursor: pointer;">
                Place Order
            </button>
            
        </div>
        
    </div>
    
    <p style="text-align: center; margin-top: 30px; color: #666;">
        Si vous voyez ce panel à 100% de zoom, le problème vient du fichier principal.<br>
        Si vous ne le voyez pas, le problème vient de votre navigateur ou écran.
    </p>
    
</body>
</html>
