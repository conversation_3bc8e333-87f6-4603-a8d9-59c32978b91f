<?php
/**
 * Test des modifications pour permettre la sélection de produits avec stock insuffisant
 */

require_once 'pos_config.php';

// Démarrer la session
session_start();

// Créer une instance de POS
$pos = new POSConfig();

echo "<h1>Test des modifications - Stock insuffisant</h1>";

// Test 1: Récupérer quelques articles
echo "<h2>1. Articles disponibles</h2>";
$articles = $pos->getArticlesByCategory(null, true); // Inclure les articles avec stock 0
echo "<p>Nombre d'articles trouvés: " . count($articles) . "</p>";

if (!empty($articles)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Nom</th><th>Stock</th><th>Catégorie</th><th>Test Ajout</th></tr>";
    
    foreach (array_slice($articles, 0, 5) as $article) {
        echo "<tr>";
        echo "<td>" . $article['IDarticles'] . "</td>";
        echo "<td>" . htmlspecialchars($article['designation']) . "</td>";
        echo "<td style='color: " . ($article['quantite'] <= 0 ? 'red' : 'green') . "'>" . $article['quantite'] . "</td>";
        echo "<td>" . htmlspecialchars($article['nom_categorie']) . "</td>";
        
        // Test d'ajout au panier
        $result = $pos->addToCart($article['IDarticles'], 1);
        if ($result['success']) {
            $status = $result['stock_insufficient'] ? 
                "<span style='color: orange;'>✓ Ajouté (stock insuffisant)</span>" : 
                "<span style='color: green;'>✓ Ajouté</span>";
        } else {
            $status = "<span style='color: red;'>✗ " . $result['message'] . "</span>";
        }
        echo "<td>" . $status . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test 2: Vérifier le contenu du panier
echo "<h2>2. Contenu du panier après tests</h2>";
$cart = $pos->getCart();
if (!empty($cart)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Article</th><th>Quantité</th><th>Stock disponible</th><th>Stock insuffisant?</th></tr>";
    
    foreach ($cart as $articleId => $item) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($item['article']['designation']) . "</td>";
        echo "<td>" . $item['quantity'] . "</td>";
        echo "<td>" . $item['article']['quantite'] . "</td>";
        echo "<td>" . (isset($item['stock_insufficient']) && $item['stock_insufficient'] ? 
            "<span style='color: red;'>Oui ⚠️</span>" : 
            "<span style='color: green;'>Non ✓</span>") . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>Total du panier:</strong> " . number_format($pos->getCartTotalTTC(), 2) . " €</p>";
} else {
    echo "<p>Le panier est vide.</p>";
}

// Test 3: Test spécifique avec un article à stock 0
echo "<h2>3. Test spécifique - Article avec stock 0</h2>";
$articlesZeroStock = array_filter($articles, function($art) {
    return $art['quantite'] <= 0;
});

if (!empty($articlesZeroStock)) {
    $articleTest = reset($articlesZeroStock);
    echo "<p>Test avec l'article: <strong>" . htmlspecialchars($articleTest['designation']) . "</strong> (Stock: " . $articleTest['quantite'] . ")</p>";
    
    // Vider le panier d'abord
    $pos->clearCart();
    
    // Tenter d'ajouter l'article
    $result = $pos->addToCart($articleTest['IDarticles'], 2);
    
    echo "<p>Résultat de l'ajout de 2 unités:</p>";
    echo "<ul>";
    echo "<li>Succès: " . ($result['success'] ? 'Oui' : 'Non') . "</li>";
    echo "<li>Message: " . $result['message'] . "</li>";
    echo "<li>Stock insuffisant: " . (isset($result['stock_insufficient']) && $result['stock_insufficient'] ? 'Oui' : 'Non') . "</li>";
    echo "</ul>";
    
    if ($result['success']) {
        echo "<p style='color: green;'><strong>✓ SUCCÈS:</strong> L'article avec stock insuffisant a pu être ajouté au panier!</p>";
    } else {
        echo "<p style='color: red;'><strong>✗ ÉCHEC:</strong> L'article n'a pas pu être ajouté.</p>";
    }
} else {
    echo "<p>Aucun article avec stock 0 trouvé pour le test.</p>";
}

// Nettoyer
$pos->clearCart();

echo "<h2>4. Résumé des modifications</h2>";
echo "<ul>";
echo "<li>✓ Méthode addToCart() modifiée pour permettre l'ajout avec stock insuffisant</li>";
echo "<li>✓ Retour d'informations détaillées (succès, message, indicateur de stock)</li>";
echo "<li>✓ Méthode updateCartQuantity() mise à jour</li>";
echo "<li>✓ Interface utilisateur modifiée avec indicateurs visuels</li>";
echo "<li>✓ Gestion des avertissements côté client</li>";
echo "</ul>";

echo "<p><a href='pos_mobile.php'>→ Tester l'interface POS Mobile</a></p>";
?>
