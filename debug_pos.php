<?php
require_once 'pos_config.php';

echo "<h1>🔍 Debug POS - Diagnostic des données</h1>";

// Test de connexion
echo "<h2>1. Test de connexion HFSQL</h2>";
if ($pos->isConnected()) {
    echo "<div style='color: green; padding: 10px; background: #d4edda; border-radius: 5px;'>✅ Connexion HFSQL réussie</div>";
} else {
    echo "<div style='color: red; padding: 10px; background: #f8d7da; border-radius: 5px;'>❌ Connexion HFSQL échouée</div>";
    exit;
}

// Test des catégories
echo "<h2>2. Test des catégories</h2>";
$categories = $pos->getCategories();
echo "<p><strong>Nombre de catégories :</strong> " . count($categories) . "</p>";

if (!empty($categories)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Nom</th><th>Photo</th></tr>";
    foreach ($categories as $cat) {
        echo "<tr>";
        echo "<td>" . $cat['IDCategorie'] . "</td>";
        echo "<td>" . htmlspecialchars($cat['categories']) . "</td>";
        echo "<td>" . htmlspecialchars($cat['photo']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<div style='color: red;'>❌ Aucune catégorie trouvée</div>";
}

// Test des articles
echo "<h2>3. Test des articles</h2>";
$articles = $pos->getArticlesByCategory();
echo "<p><strong>Nombre d'articles :</strong> " . count($articles) . "</p>";

if (!empty($articles)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Désignation</th><th>Catégorie ID</th><th>Nom Catégorie</th><th>Quantité</th></tr>";
    foreach (array_slice($articles, 0, 10) as $art) {
        echo "<tr>";
        echo "<td>" . $art['IDarticles'] . "</td>";
        echo "<td>" . htmlspecialchars($art['designation']) . "</td>";
        echo "<td>" . $art['IDCategorie'] . "</td>";
        echo "<td>" . htmlspecialchars($art['nom_categorie'] ?? 'N/A') . "</td>";
        echo "<td>" . $art['quantite'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (count($articles) > 10) {
        echo "<p><em>... et " . (count($articles) - 10) . " autres articles</em></p>";
    }
} else {
    echo "<div style='color: red;'>❌ Aucun article trouvé</div>";
}

// Test des articles avec stock > 0
echo "<h2>4. Test des articles avec stock</h2>";
$articlesWithStock = array_filter($articles, function($art) {
    return $art['quantite'] > 0;
});
echo "<p><strong>Articles avec stock > 0 :</strong> " . count($articlesWithStock) . "</p>";

// Test par catégorie
echo "<h2>5. Test par catégorie</h2>";
foreach ($categories as $cat) {
    $catArticles = $pos->getArticlesByCategory($cat['IDCategorie']);
    $catArticlesWithStock = array_filter($catArticles, function($art) {
        return $art['quantite'] > 0;
    });
    
    echo "<p><strong>" . htmlspecialchars($cat['categories']) . " (ID: " . $cat['IDCategorie'] . ")</strong> : ";
    echo count($catArticles) . " articles total, " . count($catArticlesWithStock) . " avec stock</p>";
}

// Test de la requête SQL directe
echo "<h2>6. Test requête SQL directe</h2>";
try {
    $pdo = new PDO("odbc:DataCafe", "admin", "");
    
    // Test table articles
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM articles");
    $result = $stmt->fetch();
    echo "<p><strong>Total articles dans la base :</strong> " . $result['total'] . "</p>";
    
    // Test articles avec quantité
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM articles WHERE quantite > 0");
    $result = $stmt->fetch();
    echo "<p><strong>Articles avec stock > 0 :</strong> " . $result['total'] . "</p>";
    
    // Test jointure
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM articles a LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie");
    $result = $stmt->fetch();
    echo "<p><strong>Articles avec jointure catégorie :</strong> " . $result['total'] . "</p>";
    
    // Quelques exemples d'articles
    echo "<h3>Exemples d'articles :</h3>";
    $stmt = $pdo->query("SELECT TOP 5 a.IDarticles, a.designation, a.quantite, a.IDCategorie, c.categories 
                         FROM articles a 
                         LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie 
                         ORDER BY a.IDarticles");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Désignation</th><th>Quantité</th><th>Cat ID</th><th>Catégorie</th></tr>";
    while ($row = $stmt->fetch()) {
        echo "<tr>";
        echo "<td>" . $row['IDarticles'] . "</td>";
        echo "<td>" . htmlspecialchars($row['designation']) . "</td>";
        echo "<td>" . $row['quantite'] . "</td>";
        echo "<td>" . $row['IDCategorie'] . "</td>";
        echo "<td>" . htmlspecialchars($row['categories'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Erreur SQL : " . htmlspecialchars($e->getMessage()) . "</div>";
}

// Test des paramètres GET
echo "<h2>7. Paramètres de la page</h2>";
echo "<p><strong>Catégorie sélectionnée :</strong> " . ($_GET['category'] ?? 'Aucune') . "</p>";
echo "<p><strong>Recherche :</strong> " . ($_GET['search'] ?? 'Aucune') . "</p>";

// Test de la méthode getArticlesByCategory avec différents paramètres
echo "<h2>8. Test méthodes POS</h2>";

echo "<h3>getArticlesByCategory() - sans paramètre :</h3>";
$test1 = $pos->getArticlesByCategory();
echo "<p>Résultat : " . count($test1) . " articles</p>";

echo "<h3>getArticlesByCategory(null) - avec null :</h3>";
$test2 = $pos->getArticlesByCategory(null);
echo "<p>Résultat : " . count($test2) . " articles</p>";

if (!empty($categories)) {
    $firstCat = $categories[0]['IDCategorie'];
    echo "<h3>getArticlesByCategory($firstCat) - première catégorie :</h3>";
    $test3 = $pos->getArticlesByCategory($firstCat);
    echo "<p>Résultat : " . count($test3) . " articles</p>";
}

echo "<h2>9. Variables de session</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>10. Configuration</h2>";
echo "<p><strong>DSN :</strong> " . POS_DSN . "</p>";
echo "<p><strong>Username :</strong> " . POS_USERNAME . "</p>";
echo "<p><strong>PHP Version :</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Extensions chargées :</strong></p>";
echo "<ul>";
echo "<li>PDO : " . (extension_loaded('pdo') ? 'Oui' : 'Non') . "</li>";
echo "<li>PDO ODBC : " . (extension_loaded('pdo_odbc') ? 'Oui' : 'Non') . "</li>";
echo "<li>ODBC : " . (extension_loaded('odbc') ? 'Oui' : 'Non') . "</li>";
echo "</ul>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #2c3e50; }
table { margin: 10px 0; }
th { background: #f8f9fa; padding: 8px; }
td { padding: 8px; }
</style>
