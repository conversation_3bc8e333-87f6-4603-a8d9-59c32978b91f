<?php
require_once 'pos_config.php';

// Debug des paramètres
$selectedCategory = $_GET['category'] ?? '';
$searchQuery = $_GET['search'] ?? '';

echo "<!DOCTYPE html><html><head><title>POS Debug Test</title></head><body>";
echo "<h1>POS Debug Test</h1>";

// Test de connexion
echo "<h2>1. Connexion</h2>";
if ($pos->isConnected()) {
    echo "<p style='color: green;'>✅ Connexion OK</p>";
} else {
    echo "<p style='color: red;'>❌ Connexion échouée</p>";
    exit;
}

// Test des catégories
echo "<h2>2. Catégories</h2>";
$categories = $pos->getCategories();
echo "<p>Nombre de catégories : " . count($categories) . "</p>";

// Test des articles
echo "<h2>3. Articles</h2>";
echo "<p>Paramètres :</p>";
echo "<ul>";
echo "<li>Catégorie sélectionnée : '" . htmlspecialchars($selectedCategory) . "'</li>";
echo "<li>Recherche : '" . htmlspecialchars($searchQuery) . "'</li>";
echo "</ul>";

// Récupération des articles selon la logique du POS
if ($searchQuery) {
    echo "<p>Mode : Recherche</p>";
    $articles = $pos->searchArticles($searchQuery);
} else {
    echo "<p>Mode : Par catégorie</p>";
    $categoryParam = $selectedCategory ?: null;
    echo "<p>Paramètre catégorie passé : " . ($categoryParam ? $categoryParam : 'null') . "</p>";
    $articles = $pos->getArticlesByCategory($categoryParam);
}

echo "<p><strong>Nombre d'articles trouvés : " . count($articles) . "</strong></p>";

if (!empty($articles)) {
    echo "<h3>Articles trouvés :</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Désignation</th><th>Quantité</th><th>Catégorie</th></tr>";
    
    foreach (array_slice($articles, 0, 10) as $article) {
        echo "<tr>";
        echo "<td>" . $article['IDarticles'] . "</td>";
        echo "<td>" . htmlspecialchars($article['designation']) . "</td>";
        echo "<td>" . $article['quantite'] . "</td>";
        echo "<td>" . htmlspecialchars($article['nom_categorie'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (count($articles) > 10) {
        echo "<p><em>... et " . (count($articles) - 10) . " autres</em></p>";
    }
} else {
    echo "<p style='color: red;'>❌ Aucun article trouvé</p>";
    
    // Test direct SQL
    echo "<h3>Test SQL direct :</h3>";
    try {
        $pdo = new PDO("odbc:DataCafe", "admin", "");
        
        if ($selectedCategory) {
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM articles WHERE IDCategorie = ?");
            $stmt->execute([$selectedCategory]);
            $result = $stmt->fetch();
            echo "<p>Articles dans la catégorie $selectedCategory : " . $result['total'] . "</p>";
        } else {
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM articles");
            $result = $stmt->fetch();
            echo "<p>Total articles : " . $result['total'] . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Erreur SQL : " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Test des liens de navigation
echo "<h2>4. Test de navigation</h2>";
echo "<p><a href='?'>Tous les articles</a></p>";

foreach ($categories as $cat) {
    echo "<p><a href='?category=" . $cat['IDCategorie'] . "'>" . htmlspecialchars($cat['categories']) . " (ID: " . $cat['IDCategorie'] . ")</a></p>";
}

echo "<p><a href='?search=café'>Recherche 'café'</a></p>";

echo "</body></html>";
?>
