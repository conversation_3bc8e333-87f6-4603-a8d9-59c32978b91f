<?php
// POS Moderne - Structure Foodeology
session_start();

try {
    $pdo = new PDO("odbc:DataCafe", "admin", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die("Erreur connexion : " . $e->getMessage());
}

// Récupération des données
$selectedCategory = $_GET['category'] ?? '';
$currentPage = $_GET['page'] ?? 'menu';

// Catégories avec icônes
$categories = [];
try {
    $stmt = $pdo->query("SELECT IDCategorie, categories FROM Categorie ORDER BY categories");
    $categoriesData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Mapping des icônes par catégorie
    $categoryIcons = [
        'Pizza' => '🍕',
        'Burger' => '🍔',
        'Dessert' => '🍰',
        '<PERSON>son' => '🥤',
        'Salade' => '🥗',
        'Sandwich' => '🥪',
        'Café' => '☕',
        'Thé' => '🍵'
    ];

    foreach ($categoriesData as $cat) {
        $categories[] = [
            'id' => $cat['IDCategorie'],
            'name' => $cat['categories'],
            'icon' => $categoryIcons[$cat['categories']] ?? '🍽️'
        ];
    }
} catch (Exception $e) {
    echo "Erreur catégories : " . $e->getMessage();
}

// Debug des catégories
if (isset($_GET['debug'])) {
    echo "<pre>Debug Catégories: " . print_r($categories, true) . "</pre>";
}

// Articles avec prix simulés
$articles = [];
try {
    if ($selectedCategory) {
        $stmt = $pdo->prepare("SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, c.categories as nom_categorie
                               FROM articles a
                               LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                               WHERE a.IDCategorie = ?
                               ORDER BY a.designation");
        $stmt->execute([$selectedCategory]);
    } else {
        $stmt = $pdo->query("SELECT a.IDarticles, a.designation, a.quantite, a.IDCategorie, c.categories as nom_categorie
                             FROM articles a
                             LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie
                             ORDER BY a.designation");
    }
    $articlesData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Ajout de prix simulés
    foreach ($articlesData as $article) {
        $articles[] = [
            'id' => $article['IDarticles'],
            'name' => $article['designation'],
            'category' => $article['nom_categorie'],
            'stock' => $article['quantite'],
            'price' => rand(8, 25) . '.00', // Prix simulé
            'image' => 'https://via.placeholder.com/150x150/FF6B6B/FFFFFF?text=' . urlencode(substr($article['designation'], 0, 3))
        ];
    }
} catch (Exception $e) {
    echo "Erreur articles : " . $e->getMessage();
}

// Gestion du panier
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Traitement des actions AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    switch ($_POST['action']) {
        case 'add_to_cart':
            $articleId = $_POST['article_id'];
            $quantity = $_POST['quantity'] ?? 1;

            if (!isset($_SESSION['cart'][$articleId])) {
                $_SESSION['cart'][$articleId] = 0;
            }
            $_SESSION['cart'][$articleId] += $quantity;

            echo json_encode(['success' => true, 'cart_count' => array_sum($_SESSION['cart'])]);
            exit;

        case 'remove_from_cart':
            $articleId = $_POST['article_id'];
            unset($_SESSION['cart'][$articleId]);
            echo json_encode(['success' => true, 'cart_count' => array_sum($_SESSION['cart'])]);
            exit;

        case 'clear_cart':
            $_SESSION['cart'] = [];
            echo json_encode(['success' => true]);
            exit;
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BeCoffee POS - Point de Vente</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            overflow-x: hidden;
        }

        /* Layout Principal */
        .app-container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 25px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 45px;
            height: 45px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .logo-text h2 {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .logo-text p {
            font-size: 12px;
            opacity: 0.8;
        }

        .sidebar-nav {
            flex: 1;
            padding: 20px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-item:hover,
        .nav-item.active {
            background: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #ff6b6b;
        }

        .nav-item i {
            width: 20px;
            margin-right: 15px;
            font-size: 16px;
        }

        .nav-item span {
            font-weight: 500;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ff6b6b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user-details h4 {
            font-size: 14px;
            margin-bottom: 2px;
        }

        .user-details p {
            font-size: 12px;
            opacity: 0.7;
        }

        /* Zone Principale */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #f8f9fa;
        }

        /* Header Principal */
        .main-header {
            background: white;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .header-left h1 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
            flex: 1;
            justify-content: space-between;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 10px 15px 10px 40px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            width: 300px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }



        .categories-nav {
            display: flex;
            gap: 8px;
            min-width: max-content;
            overflow-x: auto;
            padding: 5px 0;
        }

        .category-tab {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 15px;
            background: #f8f9fa;
            border: 2px solid transparent;
            border-radius: 8px;
            text-decoration: none;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
            white-space: nowrap;
            font-size: 13px;
        }

        .category-tab:hover,
        .category-tab.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .category-tab .icon {
            font-size: 14px;
        }

        /* FORCER LA GRILLE 3 COLONNES */
        .articles-grid-force {
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 15px !important;
            width: 100% !important;
        }

        .article-card-force {
            width: 100% !important;
            max-width: none !important;
            min-width: 0 !important;
            box-sizing: border-box !important;
        }

        /* Panel de Commande - POSITION FORCÉE */
        .order-panel {
            width: 350px !important;
            min-width: 350px !important;
            max-width: 350px !important;
            flex: 0 0 350px !important;
            background: white !important;
            border-radius: 12px !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
            display: flex !important;
            flex-direction: column !important;
            max-height: calc(100vh - 200px) !important;
            position: sticky !important;
            top: 20px !important;
            align-self: flex-start !important;
        }

        .panel-header {
            padding: 25px;
            border-bottom: 1px solid #e9ecef;
        }

        .panel-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .panel-title h3 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
        }

        .order-number {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .customer-info {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .customer-avatar {
            width: 40px;
            height: 40px;
            background: #ff6b6b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .customer-details h4 {
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .customer-details p {
            color: #6c757d;
            font-size: 12px;
        }

        /* Liste des Articles dans le Panier */
        .cart-items {
            flex: 1;
            padding: 20px 25px;
            overflow-y: auto;
        }

        .cart-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .item-image {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .item-price {
            color: #667eea;
            font-weight: 500;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .qty-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #e9ecef;
            background: white;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .qty-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .qty-display {
            min-width: 30px;
            text-align: center;
            font-weight: 600;
        }

        .remove-item {
            color: #dc3545;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.3s;
        }

        .remove-item:hover {
            background: #f8d7da;
        }

        /* Résumé de Commande */
        .order-summary {
            padding: 25px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .summary-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            color: #6c757d;
        }

        .summary-line.total {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
            padding-top: 10px;
            border-top: 1px solid #e9ecef;
            margin-top: 15px;
        }

        /* Méthodes de Paiement */
        .payment-methods {
            margin: 20px 0;
        }

        .payment-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .payment-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .payment-option {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: white;
        }

        .payment-option:hover,
        .payment-option.active {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .payment-option i {
            font-size: 20px;
            margin-bottom: 5px;
            color: #667eea;
        }

        .payment-option span {
            display: block;
            font-size: 12px;
            color: #6c757d;
        }

        /* Bouton de Commande */
        .place-order-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .place-order-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
        }

        .place-order-btn:disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* États Vides */
        .empty-cart {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-cart i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-cart h3 {
            margin-bottom: 8px;
        }

        .empty-cart p {
            font-size: 14px;
        }

        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .cart-item {
            animation: slideIn 0.3s ease;
        }

        /* RESPONSIVE SUPPRIMÉ - GRILLE 3 COLONNES FORCÉE */

        /* Utilitaires */
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .mb-0 { margin-bottom: 0; }
        .mt-20 { margin-top: 20px; }
        .hidden { display: none; }

        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background: #f8f9fa;">

    <!-- CONTAINER PRINCIPAL -->
    <div style="display: flex; height: 100vh;">

        <!-- SIDEBAR NAVIGATION -->
        <div style="width: 250px; background: #2c3e50; color: white; display: flex; flex-direction: column;">

            <!-- LOGO -->
            <div style="padding: 25px 20px; border-bottom: 1px solid #34495e;">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #ff6b6b, #ffa500); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 20px;">☕</div>
                    <div>
                        <h2 style="margin: 0; font-size: 18px; font-weight: 600;">BeCoffee</h2>
                        <p style="margin: 0; font-size: 12px; opacity: 0.8;">Point de Vente</p>
                    </div>
                </div>
            </div>

            <!-- MENU NAVIGATION -->
            <nav style="flex: 1; padding: 20px 0;">
                <a href="?page=pos" style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: white; text-decoration: none; background: #34495e; border-right: 3px solid #ff6b6b;">
                    <span style="font-size: 16px;">🛒</span>
                    <span>Point de Vente</span>
                </a>
                <a href="?page=menu" style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: white; text-decoration: none; opacity: 0.8;">
                    <span style="font-size: 16px;">🍽️</span>
                    <span>Menu</span>
                </a>
                <a href="?page=orders" style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: white; text-decoration: none; opacity: 0.8;">
                    <span style="font-size: 16px;">📋</span>
                    <span>Commandes</span>
                </a>
                <a href="?page=history" style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: white; text-decoration: none; opacity: 0.8;">
                    <span style="font-size: 16px;">📊</span>
                    <span>Historique</span>
                </a>
                <a href="?page=analytics" style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: white; text-decoration: none; opacity: 0.8;">
                    <span style="font-size: 16px;">📈</span>
                    <span>Analyses</span>
                </a>
                <a href="?page=settings" style="display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: white; text-decoration: none; opacity: 0.8;">
                    <span style="font-size: 16px;">⚙️</span>
                    <span>Paramètres</span>
                </a>
            </nav>

        </div>

        <!-- ZONE PRINCIPALE -->
        <div style="flex: 1; display: flex; flex-direction: column;">

            <!-- HEADER AVEC CATÉGORIES -->
            <div style="background: white; padding: 20px; border-bottom: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">

                <!-- TITRE ET RECHERCHE -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1 style="margin: 0; color: #2c3e50; font-size: 24px; font-weight: 600;">Point de Vente</h1>
                    <div style="position: relative;">
                        <input type="text" placeholder="Rechercher un article..." style="width: 300px; padding: 10px 40px 10px 15px; border: 1px solid #e9ecef; border-radius: 8px; font-size: 14px;">
                        <span style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #6c757d;">🔍</span>
                    </div>
                </div>

                <!-- CATÉGORIES DYNAMIQUES -->
                <div style="display: flex; gap: 10px; overflow-x: auto; padding: 5px 0;">

                    <!-- BOUTON TOUS -->
                    <a href="?" style="text-decoration: none;">
                        <button style="padding: 8px 16px; <?php echo empty($selectedCategory) ? 'background: linear-gradient(135deg, #ff6b6b, #ffa500); color: white;' : 'background: #f8f9fa; color: #6c757d; border: 1px solid #e9ecef;'; ?> border: none; border-radius: 20px; font-size: 14px; font-weight: 500; cursor: pointer; white-space: nowrap; display: flex; align-items: center; gap: 5px;">
                            <span>🍽️ Tous</span>
                            <span style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px; font-size: 11px; font-weight: bold;">
                                <?php echo count($articles); ?>
                            </span>
                        </button>
                    </a>

                    <!-- CATÉGORIES DE LA BASE DE DONNÉES -->
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category): ?>
                            <?php
                            // Compter les articles de cette catégorie
                            $categoryCount = 0;
                            foreach ($articles as $article) {
                                if ($article['category_id'] == $category['id']) {
                                    $categoryCount++;
                                }
                            }
                            ?>
                            <a href="?category=<?php echo $category['id']; ?>" style="text-decoration: none;">
                                <button style="padding: 8px 16px; <?php echo $selectedCategory == $category['id'] ? 'background: linear-gradient(135deg, #ff6b6b, #ffa500); color: white;' : 'background: #f8f9fa; color: #6c757d; border: 1px solid #e9ecef;'; ?> border: none; border-radius: 20px; font-size: 14px; font-weight: 500; cursor: pointer; white-space: nowrap; display: flex; align-items: center; gap: 5px;">
                                    <span><?php echo $category['icon']; ?> <?php echo htmlspecialchars($category['name']); ?></span>
                                    <span style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px; font-size: 11px; font-weight: bold;">
                                        <?php echo $categoryCount; ?>
                                    </span>
                                </button>
                            </a>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- MESSAGE SI AUCUNE CATÉGORIE -->
                        <div style="padding: 8px 15px; color: #dc3545; background: #f8d7da; border-radius: 20px; font-size: 12px; white-space: nowrap;">
                            ⚠️ Aucune catégorie trouvée
                        </div>
                    <?php endif; ?>

                </div>

            </div>

            <!-- INTERFACE POS -->
            <div style="flex: 1; display: flex; padding: 20px; gap: 20px;">

                <!-- SECTION ARTICLES (60%) -->
                <div style="flex: 0 0 60%; background: #f8f9fa; border-radius: 15px; padding: 20px; overflow-y: auto;">

                <!-- GRILLE 3 COLONNES SIMPLE -->
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">

                    <?php if (!empty($articles)): ?>
                        <?php foreach ($articles as $article): ?>
                            <div style="background: white; border-radius: 12px; padding: 20px; text-align: center; box-shadow: 0 3px 12px rgba(0,0,0,0.08); cursor: pointer; height: 250px; display: flex; flex-direction: column; justify-content: space-between;">

                                <!-- IMAGE CIRCULAIRE -->
                                <div style="width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 15px; background: linear-gradient(135deg, #ff6b6b, #ffa500); display: flex; align-items: center; justify-content: center; font-size: 30px; color: white;">
                                    🍕
                                </div>

                                <!-- INFOS PRODUIT -->
                                <div>
                                    <h3 style="font-size: 16px; font-weight: 600; color: #2c3e50; margin: 0 0 8px 0;"><?php echo htmlspecialchars($article['name']); ?></h3>
                                    <div style="font-size: 18px; font-weight: 700; color: #667eea; margin-bottom: 15px;">$<?php echo $article['price']; ?></div>
                                    <div style="font-size: 12px; color: #28a745; margin-bottom: 15px;">Stock: <?php echo $article['stock']; ?></div>
                                </div>

                                <!-- CONTROLES QUANTITE -->
                                <div style="display: flex; align-items: center; justify-content: center; gap: 10px; margin-bottom: 15px;">
                                    <button style="width: 30px; height: 30px; border: 1px solid #e9ecef; background: white; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; color: #6c757d;">-</button>
                                    <span style="min-width: 20px; text-align: center; font-weight: 600;">1</span>
                                    <button style="width: 30px; height: 30px; border: 1px solid #e9ecef; background: white; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; color: #6c757d;">+</button>
                                </div>

                                <!-- BOUTON AJOUTER -->
                                <button style="width: 100%; padding: 12px; background: linear-gradient(135deg, #ff6b6b, #ffa500); color: white; border: none; border-radius: 10px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;">
                                    Added
                                </button>

                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #6c757d;">
                            <h3>Aucun article trouvé</h3>
                            <p>Vérifiez votre base de données</p>
                        </div>
                    <?php endif; ?>

                </div>

            </div>

            <!-- PANEL COMMANDE (40%) -->
            <div style="flex: 0 0 40%; width: 40%; min-width: 350px; background: white; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); display: flex; flex-direction: column; height: 100%;">

                <!-- HEADER COMMANDE -->
                <div style="padding: 25px 25px 20px 25px; border-bottom: 1px solid #f1f3f4;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3 style="color: #2c3e50; font-size: 18px; font-weight: 600; margin: 0;">New Order Bill</h3>
                        <span style="background: #667eea; color: white; padding: 5px 12px; border-radius: 20px; font-size: 12px; font-weight: 500;">Order #246</span>
                    </div>
                </div>

                <!-- LISTE ARTICLES COMMANDE -->
                <div style="flex: 1; padding: 20px 25px; overflow-y: auto;">

                    <!-- ARTICLE 1 -->
                    <div style="display: flex; align-items: center; gap: 15px; padding: 15px 0; border-bottom: 1px solid #f1f3f4;">
                        <div style="width: 50px; height: 50px; border-radius: 8px; background: linear-gradient(135deg, #ff6b6b, #ffa500); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">🍰</div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #2c3e50; margin-bottom: 4px;">Cup Cake</div>
                            <div style="color: #667eea; font-weight: 500;">$12.00</div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span style="background: #ffa500; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">1</span>
                            <button style="color: #dc3545; background: none; border: none; cursor: pointer; padding: 4px;">🗑️</button>
                        </div>
                    </div>

                    <!-- ARTICLE 2 -->
                    <div style="display: flex; align-items: center; gap: 15px; padding: 15px 0; border-bottom: 1px solid #f1f3f4;">
                        <div style="width: 50px; height: 50px; border-radius: 8px; background: linear-gradient(135deg, #ff6b6b, #ffa500); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">🍕</div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #2c3e50; margin-bottom: 4px;">Farm Villa</div>
                            <div style="color: #667eea; font-weight: 500;">$16.00</div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span style="background: #ffa500; color: white; width: 20px; height: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">2</span>
                            <button style="color: #dc3545; background: none; border: none; cursor: pointer; padding: 4px;">🗑️</button>
                        </div>
                    </div>

                </div>

                <!-- RESUME COMMANDE -->
                <div style="padding: 25px; border-top: 1px solid #f1f3f4; background: #f8f9fa;">

                    <!-- TOTAUX -->
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px; color: #6c757d;">
                            <span>Sub Total</span>
                            <span>$50.00</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 15px; color: #6c757d; font-size: 14px;">
                            <span>Tax 10% (VAT Included)</span>
                            <span>$5.00</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 18px; font-weight: 700; color: #2c3e50; padding-top: 15px; border-top: 1px dashed #ddd;">
                            <span>Total</span>
                            <span style="color: #28a745;">$55.00</span>
                        </div>
                    </div>

                    <!-- METHODES PAIEMENT -->
                    <div style="margin-bottom: 20px;">
                        <div style="font-weight: 600; color: #2c3e50; margin-bottom: 15px;">Payment Method</div>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                            <div style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; text-align: center; cursor: pointer; background: white;">
                                <div style="font-size: 20px; margin-bottom: 5px;">💵</div>
                                <span style="font-size: 12px; color: #6c757d;">Cash</span>
                            </div>
                            <div style="padding: 12px; border: 2px solid #e9ecef; border-radius: 8px; text-align: center; cursor: pointer; background: white;">
                                <div style="font-size: 20px; margin-bottom: 5px;">💳</div>
                                <span style="font-size: 12px; color: #6c757d;">Debit Card</span>
                            </div>
                            <div style="padding: 12px; border: 2px solid #667eea; border-radius: 8px; text-align: center; cursor: pointer; background: #f8f9ff;">
                                <div style="font-size: 20px; margin-bottom: 5px;">📱</div>
                                <span style="font-size: 12px; color: #667eea; font-weight: 600;">E-Wallet</span>
                            </div>
                        </div>
                    </div>

                    <!-- BOUTON COMMANDE -->
                    <button style="width: 100%; padding: 15px; background: linear-gradient(135deg, #ff6b6b, #ffa500); color: white; border: none; border-radius: 12px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;">
                        Place Order
                    </button>

                </div>

                </div>

            </div>

        </div>

    </div>

    <script>
        // État global du panier
        let cart = {};
        let currentPaymentMethod = 'cash';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateCartDisplay();
        });

        function initializeEventListeners() {
            // Recherche
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', handleSearch);
            }

            // Méthodes de paiement
            document.querySelectorAll('.payment-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.payment-option').forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    currentPaymentMethod = this.dataset.method;
                });
            });

            // Bouton de commande
            const placeOrderBtn = document.getElementById('placeOrderBtn');
            if (placeOrderBtn) {
                placeOrderBtn.addEventListener('click', placeOrder);
            }

            // Toggle de vue
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.toggle-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    const view = this.dataset.view;
                    const grid = document.getElementById('articlesGrid');
                    if (grid) {
                        grid.className = view === 'list' ? 'articles-list' : 'articles-grid';
                    }
                });
            });
        }

        function addToCart(articleId, articleName, price) {
            if (!cart[articleId]) {
                cart[articleId] = {
                    id: articleId,
                    name: articleName,
                    price: parseFloat(price),
                    quantity: 0
                };
            }

            cart[articleId].quantity += 1;
            updateCartDisplay();

            // Animation de feedback
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> Ajouté !';
            button.style.background = '#28a745';

            setTimeout(() => {
                button.innerHTML = originalText;
                button.style.background = '';
            }, 1000);
        }

        function removeFromCart(articleId) {
            delete cart[articleId];
            updateCartDisplay();
        }

        function updateQuantity(articleId, change) {
            if (cart[articleId]) {
                cart[articleId].quantity += change;
                if (cart[articleId].quantity <= 0) {
                    delete cart[articleId];
                }
                updateCartDisplay();
            }
        }

        function updateCartDisplay() {
            const cartItemsContainer = document.getElementById('cartItems');
            const subtotalElement = document.getElementById('subtotal');
            const taxElement = document.getElementById('tax');
            const totalElement = document.getElementById('total');
            const placeOrderBtn = document.getElementById('placeOrderBtn');

            if (!cartItemsContainer) return;

            const cartItems = Object.values(cart);

            if (cartItems.length === 0) {
                cartItemsContainer.innerHTML = `
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>Panier vide</h3>
                        <p>Ajoutez des articles pour commencer</p>
                    </div>
                `;

                if (subtotalElement) subtotalElement.textContent = '$0.00';
                if (taxElement) taxElement.textContent = '$0.00';
                if (totalElement) totalElement.textContent = '$0.00';
                if (placeOrderBtn) placeOrderBtn.disabled = true;

                return;
            }

            let html = '';
            let subtotal = 0;

            cartItems.forEach(item => {
                const itemTotal = item.price * item.quantity;
                subtotal += itemTotal;

                html += `
                    <div class="cart-item">
                        <div class="item-image">
                            ${item.name.charAt(0).toUpperCase()}
                        </div>
                        <div class="item-details">
                            <div class="item-name">${item.name}</div>
                            <div class="item-price">$${item.price.toFixed(2)}</div>
                        </div>
                        <div class="quantity-controls">
                            <button class="qty-btn" onclick="updateQuantity(${item.id}, -1)">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span class="qty-display">${item.quantity}</span>
                            <button class="qty-btn" onclick="updateQuantity(${item.id}, 1)">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="remove-item" onclick="removeFromCart(${item.id})">
                            <i class="fas fa-trash"></i>
                        </div>
                    </div>
                `;
            });

            cartItemsContainer.innerHTML = html;

            const tax = subtotal * 0.1;
            const total = subtotal + tax;

            if (subtotalElement) subtotalElement.textContent = `$${subtotal.toFixed(2)}`;
            if (taxElement) taxElement.textContent = `$${tax.toFixed(2)}`;
            if (totalElement) totalElement.textContent = `$${total.toFixed(2)}`;
            if (placeOrderBtn) placeOrderBtn.disabled = false;
        }

        function handleSearch(event) {
            const searchTerm = event.target.value.toLowerCase();
            const articleCards = document.querySelectorAll('.article-card');

            articleCards.forEach(card => {
                const articleName = card.querySelector('.article-name').textContent.toLowerCase();
                const articleCategory = card.querySelector('.article-category').textContent.toLowerCase();

                if (articleName.includes(searchTerm) || articleCategory.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function placeOrder() {
            if (Object.keys(cart).length === 0) {
                alert('Votre panier est vide !');
                return;
            }

            const orderData = {
                items: Object.values(cart),
                paymentMethod: currentPaymentMethod,
                total: Object.values(cart).reduce((sum, item) => sum + (item.price * item.quantity), 0),
                timestamp: new Date().toISOString()
            };

            // Simulation de traitement de commande
            const placeOrderBtn = document.getElementById('placeOrderBtn');
            const originalText = placeOrderBtn.innerHTML;

            placeOrderBtn.innerHTML = '<div class="spinner"></div> Traitement...';
            placeOrderBtn.disabled = true;

            setTimeout(() => {
                alert('Commande validée avec succès !');
                cart = {};
                updateCartDisplay();

                placeOrderBtn.innerHTML = originalText;
                placeOrderBtn.disabled = false;

                // Ici vous pourriez envoyer les données au serveur
                console.log('Commande:', orderData);
            }, 2000);
        }

        // Gestion des raccourcis clavier
        document.addEventListener('keydown', function(event) {
            // Ctrl+F pour focus sur la recherche
            if (event.ctrlKey && event.key === 'f') {
                event.preventDefault();
                const searchInput = document.getElementById('searchInput');
                if (searchInput) searchInput.focus();
            }

            // Échap pour vider le panier
            if (event.key === 'Escape') {
                if (Object.keys(cart).length > 0) {
                    if (confirm('Vider le panier ?')) {
                        cart = {};
                        updateCartDisplay();
                    }
                }
            }
        });
    </script>
</body>
</html>
