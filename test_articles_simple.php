<?php
// Test simple des articles
try {
    $pdo = new PDO("odbc:DataCafe", "admin", "");
    echo "<h1>Test Articles Simple</h1>";
    
    // Test 1: Compter tous les articles
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM articles");
    $result = $stmt->fetch();
    echo "<p><strong>Total articles :</strong> " . $result['total'] . "</p>";
    
    // Test 2: Lister quelques articles
    echo "<h2>Premiers articles :</h2>";
    $stmt = $pdo->query("SELECT TOP 10 IDarticles, designation, quantite, IDCategorie FROM articles ORDER BY IDarticles");
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Désignation</th><th>Quantité</th><th>Catégorie ID</th></tr>";
    
    while ($row = $stmt->fetch()) {
        echo "<tr>";
        echo "<td>" . $row['IDarticles'] . "</td>";
        echo "<td>" . htmlspecialchars($row['designation']) . "</td>";
        echo "<td>" . $row['quantite'] . "</td>";
        echo "<td>" . $row['IDCategorie'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test 3: Articles avec quantité > 0
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM articles WHERE quantite > 0");
    $result = $stmt->fetch();
    echo "<p><strong>Articles avec stock > 0 :</strong> " . $result['total'] . "</p>";
    
    // Test 4: Articles avec quantité = 0
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM articles WHERE quantite = 0");
    $result = $stmt->fetch();
    echo "<p><strong>Articles avec stock = 0 :</strong> " . $result['total'] . "</p>";
    
    // Test 5: Jointure avec catégories
    echo "<h2>Articles avec catégories :</h2>";
    $stmt = $pdo->query("SELECT TOP 10 a.IDarticles, a.designation, a.quantite, c.categories 
                         FROM articles a 
                         LEFT JOIN Categorie c ON a.IDCategorie = c.IDCategorie 
                         ORDER BY a.IDarticles");
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Désignation</th><th>Quantité</th><th>Catégorie</th></tr>";
    
    while ($row = $stmt->fetch()) {
        echo "<tr>";
        echo "<td>" . $row['IDarticles'] . "</td>";
        echo "<td>" . htmlspecialchars($row['designation']) . "</td>";
        echo "<td>" . $row['quantite'] . "</td>";
        echo "<td>" . htmlspecialchars($row['categories'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erreur : " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th { background: #f0f0f0; padding: 8px; }
td { padding: 8px; }
</style>
