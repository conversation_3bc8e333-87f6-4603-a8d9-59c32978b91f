<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interface - Stock Insuffisant</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 12px;
        }
        
        .article-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            height: 200px;
        }

        .article-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .article-card.insufficient-stock {
            border: 2px solid #e74c3c;
            opacity: 0.8;
        }

        .article-card.insufficient-stock .article-image {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .article-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .article-icon {
            font-size: 40px;
            color: white;
        }

        .stock-warning-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #e74c3c;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            z-index: 2;
        }

        .article-price-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .article-info {
            padding: 12px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .article-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.2;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .article-stock {
            font-size: 11px;
            color: #27ae60;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .article-stock.insufficient {
            color: #e74c3c;
        }

        .add-btn {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: #3498db;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .add-btn:hover {
            background: #2980b9;
            transform: scale(1.1);
        }

        .success-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(40, 167, 69, 0.95);
            color: white;
            padding: 20px 40px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            animation: successPop 0.5s ease-out;
        }
        
        .warning-animation {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 193, 7, 0.95);
            color: #212529;
            padding: 20px 40px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 1000;
            animation: warningPop 0.5s ease-out;
            border: 2px solid #ffc107;
        }
        
        @keyframes successPop {
            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }
        
        @keyframes warningPop {
            0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
        }

        .test-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        .test-btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Interface - Gestion Stock Insuffisant</h1>
        
        <div class="test-section">
            <h2>Exemples d'articles avec différents niveaux de stock</h2>
            <div class="articles-grid">
                <!-- Article avec stock normal -->
                <div class="article-card" onclick="testAddToCart(1, false)">
                    <div class="article-image">
                        <div class="article-icon">☕</div>
                        <div class="article-price-badge">2.50 €</div>
                    </div>
                    <div class="article-info">
                        <div>
                            <div class="article-name">Café Expresso</div>
                        </div>
                        <div class="article-stock">
                            <i class="fas fa-box"></i> 25
                        </div>
                    </div>
                    <button class="add-btn">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                </div>

                <!-- Article avec stock insuffisant -->
                <div class="article-card insufficient-stock" onclick="testAddToCart(2, true)">
                    <div class="stock-warning-badge">Rupture</div>
                    <div class="article-image">
                        <div class="article-icon">🥪</div>
                        <div class="article-price-badge">4.50 €</div>
                    </div>
                    <div class="article-info">
                        <div>
                            <div class="article-name">Sandwich Jambon</div>
                        </div>
                        <div class="article-stock insufficient">
                            <i class="fas fa-box"></i> 0 <span style="margin-left: 4px;">⚠️</span>
                        </div>
                    </div>
                    <button class="add-btn">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                </div>

                <!-- Article avec stock faible -->
                <div class="article-card insufficient-stock" onclick="testAddToCart(3, true)">
                    <div class="stock-warning-badge">Rupture</div>
                    <div class="article-image">
                        <div class="article-icon">🍰</div>
                        <div class="article-price-badge">3.00 €</div>
                    </div>
                    <div class="article-info">
                        <div>
                            <div class="article-name">Gâteau Chocolat</div>
                        </div>
                        <div class="article-stock insufficient">
                            <i class="fas fa-box"></i> 0 <span style="margin-left: 4px;">⚠️</span>
                        </div>
                    </div>
                    <button class="add-btn">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Tests des notifications</h2>
            <button class="test-btn" onclick="showSuccess('Article ajouté avec succès!')">Test Succès</button>
            <button class="test-btn" onclick="showWarning('Article ajouté (stock insuffisant)')">Test Avertissement</button>
            <button class="test-btn" onclick="showError('Erreur lors de l\'ajout')">Test Erreur</button>
        </div>

        <div class="test-section">
            <h2>Fonctionnalités implémentées</h2>
            <ul>
                <li>✅ <strong>Sélection autorisée</strong> même avec stock insuffisant</li>
                <li>✅ <strong>Indicateurs visuels</strong> : badge "Rupture", bordure rouge, icône d'avertissement</li>
                <li>✅ <strong>Notifications différenciées</strong> : succès (vert) vs avertissement (orange)</li>
                <li>✅ <strong>Couleur de stock</strong> : vert pour stock normal, rouge pour stock insuffisant</li>
                <li>✅ <strong>Gradient de fond</strong> : bleu pour stock normal, rouge pour stock insuffisant</li>
            </ul>
        </div>
    </div>

    <script>
        function testAddToCart(articleId, hasInsufficientStock) {
            if (hasInsufficientStock) {
                showWarning('Article ajouté (stock insuffisant)');
            } else {
                showSuccess('Article ajouté !');
            }
        }

        function showSuccess(message) {
            const div = document.createElement('div');
            div.className = 'success-animation';
            div.innerHTML = `<i class="fas fa-check"></i> ${message}`;
            document.body.appendChild(div);
            
            setTimeout(() => {
                div.remove();
            }, 3000);
        }
        
        function showWarning(message) {
            const div = document.createElement('div');
            div.className = 'warning-animation';
            div.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
            document.body.appendChild(div);
            
            setTimeout(() => {
                div.remove();
            }, 4000);
        }
        
        function showError(message) {
            alert(message);
        }
    </script>
</body>
</html>
